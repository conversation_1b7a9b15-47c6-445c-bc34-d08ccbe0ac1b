"""
Models for the User Service.

This module defines the database models for the User Service.

English: This file defines the database tables for the User Service
Tanglish: Indha file User Service-kku database tables-a define pannum
"""

from datetime import datetime
import bcrypt
import random
import string
from user_service.common.db_config import db

def generate_school_code():
    """
    Generate a unique school code in the format of 6 characters (3 letters followed by 3 numbers).

    Returns:
        A string containing 3 uppercase letters followed by 3 numbers

    English: This function generates a unique school code for school-wise separation
    Tanglish: Indha function school-wise separation-kku unique school code-a generate pannum
    """
    # Generate 3 random uppercase letters
    letters = ''.join(random.choices(string.ascii_uppercase, k=3))

    # Generate 3 random numbers
    numbers = ''.join(random.choices(string.digits, k=3))

    # Combine letters and numbers to form the school code
    school_code = f"{letters}{numbers}"

    return school_code

class User(db.Model):
    """
    User model for registration and management.

    English: This model stores user information for registration and management
    Tanglish: Indha model user information-a registration and management-kku store pannum
    """
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    role = db.Column(db.String(20), nullable=False)  # Super Admin, Admin, Teacher, Student, Parent
    is_admin = db.Column(db.Boolean, default=False)  # For Teachers who are also Admins
    course = db.Column(db.String(50), nullable=True)  # For Teachers (Neet, Jee, etc.)
    main_code = db.Column(db.String(50), nullable=True)  # For school-wise separation
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __init__(self, username, password, email, role, is_admin=False, course=None, main_code=None):
        """
        Initialize a new User.

        Args:
            username: User's username
            password: User's password (will be hashed)
            email: User's email
            role: User's role (Super Admin, Admin, Teacher, Student, Parent)
            is_admin: Whether the user is an admin (default: False)
            course: Course for Teacher (Neet, Jee, etc.) (default: None)
            main_code: School code for school-wise separation (default: None)

        English: This function creates a new user with the given information
        Tanglish: Indha function kudukkapatta information-oda puthusa oru user-a create pannum
        """
        self.username = username
        self.password = self._hash_password(password)
        self.email = email
        self.role = role
        self.is_admin = is_admin
        self.course = course

        # Generate a unique school code for Super Admin users if not provided
        if role == 'Super Admin' and main_code is None:
            self.main_code = generate_school_code()
        else:
            self.main_code = main_code

    def _hash_password(self, password):
        """
        Hash a password using bcrypt.

        Args:
            password: Plain text password

        Returns:
            Hashed password

        English: This function securely hashes the password
        Tanglish: Indha function password-a secure-a hash pannum
        """
        # Convert password to bytes if it's a string
        if isinstance(password, str):
            password = password.encode('utf-8')

        # Generate a salt and hash the password
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password, salt)

        # Return the hashed password as a string
        return hashed.decode('utf-8')

    def check_password(self, password):
        """
        Check if a password matches the stored hash.

        Args:
            password: Plain text password to check

        Returns:
            True if the password matches, False otherwise

        English: This function checks if the password is correct
        Tanglish: Indha function password correct-a irukka nu check pannum
        """
        # Convert password to bytes if it's a string
        if isinstance(password, str):
            password = password.encode('utf-8')

        # Convert stored hash to bytes if it's a string
        stored_password = self.password
        if isinstance(stored_password, str):
            stored_password = stored_password.encode('utf-8')

        # Check if the password matches
        return bcrypt.checkpw(password, stored_password)

    def to_dict(self):
        """
        Convert the user to a dictionary.

        Returns:
            Dictionary representation of the user

        English: This function converts the user to a dictionary
        Tanglish: Indha function user-a dictionary-a convert pannum
        """
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_admin': self.is_admin,
            'course': self.course,
            'main_code': self.main_code,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
